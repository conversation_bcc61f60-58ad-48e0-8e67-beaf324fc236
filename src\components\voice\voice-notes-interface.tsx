"use client";

import { useState, useEffect, useRef } from "react";
import { Upload, Play, Pause, Square, Trash2, Download, Wifi, WifiOff, FileText, Edit3, Save, X, Search, User, Calendar, Mic } from "lucide-react";

interface VoiceRecording {
  id: string;
  name: string;
  duration: number;
  size: number;
  createdAt: Date;
  isUploaded: boolean;
  localPath?: string;
  networkPath?: string;
  transcription?: string;
  notes?: string;
  patientName?: string;
  appointmentDate?: string;
  tags?: string[];
  isTranscribing?: boolean;
  isSummarizing?: boolean;
  summary?: string;
  category?: 'clinical' | 'administrative' | 'consultation' | 'other';
  provider?: string;
}

interface VoiceNotesInterfaceProps {
  isDarkMode: boolean;
}

export function VoiceNotesInterface({ isDarkMode }: VoiceNotesInterfaceProps) {
  // Only keep upload/import state
  const [recordings, setRecordings] = useState<VoiceRecording[]>([]);
  const [selectedRecording, setSelectedRecording] = useState<VoiceRecording | null>(null);
  const [isNetworkAvailable, setIsNetworkAvailable] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const audioElementRef = useRef<HTMLAudioElement | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
    // New state for enhanced functionality
  const [editingRecording, setEditingRecording] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterBy, setFilterBy] = useState<'all' | 'transcribed' | 'notes' | 'recent' | 'summarized'>('all');
  const [editNotes, setEditNotes] = useState('');
  const [editPatientName, setEditPatientName] = useState('');
  const [editAppointmentDate, setEditAppointmentDate] = useState('');
  const [editCategory, setEditCategory] = useState<'clinical' | 'administrative' | 'consultation' | 'other'>('clinical');
  const [editProvider, setEditProvider] = useState('');
  const [isPlayingAudio, setIsPlayingAudio] = useState(false);
  const [isBulkUploading, setIsBulkUploading] = useState(false);
  const [bulkProgress, setBulkProgress] = useState(0);
  const [processingQueue, setProcessingQueue] = useState<string[]>([]);
  const [showBulkModal, setShowBulkModal] = useState(false);

  // Check network availability
  useEffect(() => {
    const checkNetworkShare = async () => {
      try {
        const response = await fetch('/api/voice/network-check');
        const data = await response.json();
        setIsNetworkAvailable(data.available);
      } catch (error) {
        setIsNetworkAvailable(false);
      }
    };

    checkNetworkShare();
    const interval = setInterval(checkNetworkShare, 30000); // Check every 30 seconds
    
    return () => clearInterval(interval);
  }, []);

  // Load existing recordings
  useEffect(() => {
    loadRecordings();
  }, []);

  const loadRecordings = async () => {
    try {
      const response = await fetch('/api/voice/recordings');
      if (response.ok) {
        const data = await response.json();
        setRecordings(data.recordings || []);
      }
    } catch (error) {
      console.error('Failed to load recordings:', error);
    }
  };
  // Handle file upload (enhanced to support M4A and bulk uploads)
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files) {
      const fileArray = Array.from(files);
      
      // Check for unsupported formats
      const supportedFormats = ['.mp3', '.wav', '.m4a', '.webm', '.ogg', '.aac', '.flac'];
      const unsupportedFiles = fileArray.filter(file => 
        !supportedFormats.some(format => file.name.toLowerCase().endsWith(format))
      );
      
      if (unsupportedFiles.length > 0) {
        setError(`Unsupported file formats: ${unsupportedFiles.map(f => f.name).join(', ')}`);
        return;
      }

      // Process all files
      const newRecordings: VoiceRecording[] = fileArray.map(file => ({
        id: crypto.randomUUID(),
        name: file.name,
        duration: 0, // Will be updated when audio loads
        size: file.size,
        createdAt: new Date(),
        isUploaded: false,
        localPath: URL.createObjectURL(file),
        category: 'clinical', // Default category
        isTranscribing: false,
        isSummarizing: false
      }));
      
      setRecordings(prev => [...prev, ...newRecordings]);
      
      // If multiple files, show bulk processing modal
      if (fileArray.length > 1) {
        setShowBulkModal(true);
        setProcessingQueue(newRecordings.map(r => r.id));
      }
      
      // Upload to network if available
      if (isNetworkAvailable) {
        fileArray.forEach((file, index) => {
          uploadRecording(newRecordings[index], file);
        });
      }
    }
  };

  // Upload recording to network share
  const uploadRecording = async (recording: VoiceRecording, blob: Blob) => {
    setIsUploading(true);
    setUploadProgress(0);
    
    try {
      const formData = new FormData();
      formData.append('audio', blob, recording.name);
      formData.append('metadata', JSON.stringify({
        duration: recording.duration,
        createdAt: recording.createdAt.toISOString()
      }));
      
      const response = await fetch('/api/voice/upload', {
        method: 'POST',
        body: formData
      });
      
      if (response.ok) {
        const updatedRecording = { ...recording, isUploaded: true };
        setRecordings(prev => prev.map(r => r.id === recording.id ? updatedRecording : r));
      } else {
        throw new Error('Upload failed');
      }
    } catch (error) {
      console.error('Upload failed:', error);
      setError('Failed to upload to network share. Recording saved locally.');
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
    }
  };
  // Play/pause recording
  const togglePlayback = (recording: VoiceRecording) => {
    if (selectedRecording?.id === recording.id && audioElementRef.current?.paused === false) {
      audioElementRef.current?.pause();
      setSelectedRecording(null);
      setIsPlayingAudio(false);
    } else {
      if (audioElementRef.current) {
        audioElementRef.current.src = recording.localPath || '';
        audioElementRef.current.play();
        setSelectedRecording(recording);
        setIsPlayingAudio(true);
      }
    }
  };
  // Transcribe recording
  const transcribeRecording = async (recording: VoiceRecording) => {
    if (recording.isTranscribing) return;
    
    setRecordings(prev => prev.map(r => 
      r.id === recording.id ? { ...r, isTranscribing: true } : r
    ));
    
    try {
      const formData = new FormData();
      
      // Create a blob from the audio file if we have a local path
      if (recording.localPath) {
        const response = await fetch(recording.localPath);
        const blob = await response.blob();
        formData.append('audio', blob, recording.name);
      }
      
      const transcribeResponse = await fetch('/api/voice/transcribe', {
        method: 'POST',
        body: formData
      });
      
      if (transcribeResponse.ok) {
        const { transcription } = await transcribeResponse.json();
        setRecordings(prev => prev.map(r => 
          r.id === recording.id 
            ? { ...r, transcription, isTranscribing: false }
            : r
        ));
      } else {
        throw new Error('Transcription failed');
      }
    } catch (error) {
      console.error('Transcription failed:', error);
      setError('Failed to transcribe audio. Please try again.');
      setRecordings(prev => prev.map(r => 
        r.id === recording.id ? { ...r, isTranscribing: false } : r
      ));
    }
  };

  // Generate AI summary (privacy-safe)
  const generateSummary = async (recording: VoiceRecording) => {
    if (!recording.transcription || recording.isSummarizing) return;
    
    setRecordings(prev => prev.map(r => 
      r.id === recording.id ? { ...r, isSummarizing: true } : r
    ));
    
    try {
      const response = await fetch('/api/voice/summarize', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          transcription: recording.transcription,
          category: recording.category,
          filename: recording.name
        })
      });
      
      if (response.ok) {
        const { summary } = await response.json();
        setRecordings(prev => prev.map(r => 
          r.id === recording.id 
            ? { ...r, summary, isSummarizing: false }
            : r
        ));
      } else {
        throw new Error('Summarization failed');
      }
    } catch (error) {
      console.error('Summarization failed:', error);
      setError('Failed to generate summary. Please try again.');
      setRecordings(prev => prev.map(r => 
        r.id === recording.id ? { ...r, isSummarizing: false } : r
      ));
    }
  };

  // Bulk process recordings (transcribe + summarize)
  const processBulkRecordings = async () => {
    setIsBulkUploading(true);
    setBulkProgress(0);
    
    const recordingsToProcess = recordings.filter(r => 
      processingQueue.includes(r.id) && !r.transcription
    );
    
    for (let i = 0; i < recordingsToProcess.length; i++) {
      const recording = recordingsToProcess[i];
      
      try {
        // First transcribe
        await transcribeRecording(recording);
        
        // Wait a moment for transcription to complete
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Then summarize if transcription was successful
        const updatedRecording = recordings.find(r => r.id === recording.id);
        if (updatedRecording?.transcription) {
          await generateSummary(updatedRecording);
        }
        
        setBulkProgress(((i + 1) / recordingsToProcess.length) * 100);
      } catch (error) {
        console.error(`Failed to process recording ${recording.name}:`, error);
      }
    }
    
    setIsBulkUploading(false);
    setShowBulkModal(false);
    setProcessingQueue([]);
  };
  // Start editing a recording
  const startEditing = (recording: VoiceRecording) => {
    setEditingRecording(recording.id);
    setEditNotes(recording.notes || '');
    setEditPatientName(recording.patientName || '');
    setEditAppointmentDate(recording.appointmentDate || '');
    setEditCategory(recording.category || 'clinical');
    setEditProvider(recording.provider || '');
  };

  // Save edits
  const saveEdits = async (recordingId: string) => {
    setRecordings(prev => prev.map(r => 
      r.id === recordingId 
        ? { 
            ...r, 
            notes: editNotes,
            patientName: editPatientName,
            appointmentDate: editAppointmentDate,
            category: editCategory,
            provider: editProvider
          }
        : r
    ));
    
    setEditingRecording(null);
    setEditNotes('');
    setEditPatientName('');
    setEditAppointmentDate('');
    setEditCategory('clinical');
    setEditProvider('');
  };

  // Cancel editing
  const cancelEditing = () => {
    setEditingRecording(null);
    setEditNotes('');
    setEditPatientName('');
    setEditAppointmentDate('');
    setEditCategory('clinical');
    setEditProvider('');
  };  // Filter recordings based on search and filter criteria
  const filteredRecordings = recordings.filter(recording => {
    // Search filter
    const matchesSearch = !searchTerm || 
      recording.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      recording.transcription?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      recording.notes?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      recording.patientName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      recording.summary?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      recording.provider?.toLowerCase().includes(searchTerm.toLowerCase());
    
    // Category filter
    const matchesFilter = (() => {
      switch (filterBy) {
        case 'transcribed':
          return !!recording.transcription;
        case 'summarized':
          return !!recording.summary;
        case 'notes':
          return !!recording.notes;
        case 'recent':
          const oneWeekAgo = new Date();
          oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
          return recording.createdAt >= oneWeekAgo;
        default:
          return true;
      }
    })();
    
    return matchesSearch && matchesFilter;
  }).sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());

  // Delete recording
  const deleteRecording = async (recording: VoiceRecording) => {
    try {
      if (recording.isUploaded) {
        await fetch(`/api/voice/recordings/${recording.id}`, {
          method: 'DELETE'
        });
      }
      
      setRecordings(prev => prev.filter(r => r.id !== recording.id));
      
      if (selectedRecording?.id === recording.id) {
        setSelectedRecording(null);
      }
    } catch (error) {
      console.error('Failed to delete recording:', error);
    }
  };

  // Format time
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  // Format file size
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            Voice Notes
          </h2>
          <div className="flex items-center space-x-2">
            {isNetworkAvailable ? (
              <div className="flex items-center text-green-600 dark:text-green-400">
                <Wifi className="h-4 w-4 mr-1" />
                <span className="text-sm">Network Connected</span>
              </div>
            ) : (
              <div className="flex items-center text-orange-600 dark:text-orange-400">
                <WifiOff className="h-4 w-4 mr-1" />
                <span className="text-sm">Offline Mode</span>
              </div>
            )}
          </div>
        </div>
        
        <p className="text-gray-600 dark:text-gray-300">
          To add a voice note, connect your USB recorder and upload the file here. No in-browser recording is available.
        </p>
      </div>

      {error && (
        <div className="mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md">
          <p className="text-red-800 dark:text-red-200">{error}</p>
        </div>
      )}

      {/* Import Controls */}
      <div className="mb-8 p-6 bg-gray-50 dark:bg-gray-800 rounded-lg">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Import Voice Note
          </h3>
        </div>
        
        <div className="flex items-center space-x-4">
          <button
            onClick={() => fileInputRef.current?.click()}
            className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          >
            <Upload className="h-5 w-5 mr-2" />
            Upload Files
          </button>
          
          <input
            ref={fileInputRef}
            type="file"
            accept="audio/*"
            multiple
            onChange={handleFileUpload}
            className="hidden"
          />
        </div>
      </div>

      {/* Upload Progress */}
      {isUploading && (
        <div className="mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md">
          <div className="flex items-center justify-between mb-2">
            <span className="text-blue-800 dark:text-blue-200">Uploading to network share...</span>
            <span className="text-blue-800 dark:text-blue-200">{uploadProgress}%</span>
          </div>
          <div className="w-full bg-blue-200 dark:bg-blue-800 rounded-full h-2">
            <div
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${uploadProgress}%` }}
            />
          </div>
        </div>
      )}

      {/* Search and Filter Controls */}
      <div className="mb-6 p-4 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-700 rounded-lg">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Search recordings, transcriptions, notes..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>
          
          <div className="flex gap-2">
            <select
              value={filterBy}
              onChange={(e) => setFilterBy(e.target.value as any)}
              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Recordings</option>
              <option value="transcribed">With Transcription</option>
              <option value="notes">With Notes</option>
              <option value="recent">Recent (7 days)</option>
            </select>
          </div>
        </div>
      </div>

      {/* Recordings List */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Recordings ({recordings.length})
        </h3>
        
        {recordings.length === 0 ? (
          <div className="text-center py-8 text-gray-500 dark:text-gray-400">
            No recordings yet. Upload files from your USB recorder to get started.
          </div>
        ) : (
          <div className="space-y-3">
            {recordings.map((recording) => (
              <div
                key={recording.id}
                className="p-4 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm"
              >
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3">
                      <button
                        onClick={() => togglePlayback(recording)}
                        className="p-2 text-blue-600 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-full"
                      >
                        {selectedRecording?.id === recording.id ? (
                          <Pause className="h-5 w-5" />
                        ) : (
                          <Play className="h-5 w-5" />
                        )}
                      </button>
                      
                      <div>
                        <h4 className="font-medium text-gray-900 dark:text-white">
                          {recording.name}
                        </h4>
                        <div className="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400">
                          <span>{formatTime(recording.duration)}</span>
                          <span>{formatFileSize(recording.size)}</span>
                          <span>{recording.createdAt.toLocaleDateString()}</span>
                          {recording.isUploaded ? (
                            <span className="text-green-600 dark:text-green-400">✓ Synced</span>
                          ) : (
                            <span className="text-orange-600 dark:text-orange-400">⚠ Local only</span>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    {recording.localPath && (
                      <a
                        href={recording.localPath}
                        download={recording.name}
                        className="p-2 text-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-full"
                        title="Download"
                      >
                        <Download className="h-4 w-4" />
                      </a>
                    )}
                    
                    <button
                      onClick={() => deleteRecording(recording)}
                      className="p-2 text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-full"
                      title="Delete"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Audio Element for Playback */}
      <audio
        ref={audioElementRef}
        onEnded={() => setSelectedRecording(null)}
        className="hidden"
      />
    </div>
  );
}
