'use client';

import { useRouter } from 'next/navigation';
import { ArrowLeft } from 'lucide-react';
import { ThemeToggle } from './theme-toggle';

interface PageHeaderProps {
  title: string;
  showBackButton?: boolean;
  onBackClick?: () => void;
  backButtonLabel?: string;
  isHomePage?: boolean;
}

export function PageHeader({
  title,
  showBackButton = false,
  onBackClick,
  backButtonLabel = "Back",
  isHomePage = false
}: PageHeaderProps) {
  const router = useRouter();

  const handleTitleClick = () => {
    if (!isHomePage) {
      router.push('/');
    }
  };

  const handleBackClick = () => {
    if (onBackClick) {
      onBackClick();
    } else {
      router.back();
    }
  };

  return (
    <header className="bg-gray-100 dark:bg-gray-800 shadow">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 flex justify-between items-center">
        <div className="flex items-center space-x-4">
          {showBackButton && (
            <button
              onClick={handleBackClick}
              className="p-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white rounded-md hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
              aria-label={backButtonLabel}
            >
              <ArrowLeft className="h-5 w-5" />
            </button>
          )}
          {isHomePage ? (
            <h1 className="text-xl font-semibold text-gray-900 dark:text-white">
              {title}
            </h1>
          ) : (
            <button
              onClick={handleTitleClick}
              className="text-xl font-semibold text-gray-900 dark:text-white hover:text-gray-700 dark:hover:text-gray-300 transition-colors cursor-pointer"
            >
              {title}
            </button>
          )}
        </div>
        <ThemeToggle />
      </div>
    </header>
  );
}
