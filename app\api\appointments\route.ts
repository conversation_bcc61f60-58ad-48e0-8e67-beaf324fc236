/**
 * API route for fetching appointments
 */

import { NextRequest, NextResponse } from 'next/server';
import { SikkaApiClient } from '@/lib/api/sikka-client';
import { loadCredentials } from '@/lib/api/credentials';

// In-memory cache for API responses
const apiCache: Record<string, { data: any, timestamp: number }> = {};
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;
  const dateParam = searchParams.get('date');
  const operatories = searchParams.getAll('operatories[]');

  if (!dateParam) {
    return NextResponse.json(
      { error: 'Date parameter is required' },
      { status: 400 }
    );
  }

  // Ensure date is in the correct format (YYYY-MM-DD)
  let date = dateParam;
  try {
    // Parse the date string to ensure it's valid
    const [year, month, day] = dateParam.split('-').map(Number);
    const parsedDate = new Date(Date.UTC(year, month - 1, day, 0, 0, 0));

    // Format it back to YYYY-MM-DD to ensure consistency
    date = `${parsedDate.getUTCFullYear()}-${String(parsedDate.getUTCMonth() + 1).padStart(2, '0')}-${String(parsedDate.getUTCDate()).padStart(2, '0')}`;
  } catch (error) {
    console.error('Error parsing date:', error);
    // If there's an error parsing, use the original date string
  }

  // Create a cache key based on the request parameters
  const operatoriesKey = operatories.length > 0 ? [...operatories].sort().join('-') : 'all';
  const cacheKey = `appointments-${date}-${operatoriesKey}`;

  // Check if we have a valid cached response
  const cachedResponse = apiCache[cacheKey];
  if (cachedResponse && Date.now() - cachedResponse.timestamp < CACHE_DURATION) {
    console.log('API: Using cached appointments data');
    return NextResponse.json(cachedResponse.data);
  }

  try {
    const credentials = loadCredentials();
    const client = new SikkaApiClient(credentials);

    // Log the date and operatories being used
    console.log(`API: Fetching appointments for date: ${date}, operatories: ${operatories.join(', ') || 'none'}`);

    // Authenticate and get appointments
    await client.authenticate();
    const appointments = await client.getAppointments(date, operatories.length > 0 ? operatories : undefined);

    // Log the number of appointments found
    console.log(`API: Found ${appointments.length} appointments for date: ${date}`);

    // Log appointments by operatory
    const operatoryCounts: { [key: string]: number } = {};
    appointments.forEach(appt => {
      const op = appt.operatory || 'unknown';
      operatoryCounts[op] = (operatoryCounts[op] || 0) + 1;
    });
    console.log('API: Appointments by operatory:', JSON.stringify(operatoryCounts));

    // Cache the response
    apiCache[cacheKey] = {
      data: appointments,
      timestamp: Date.now()
    };

    return NextResponse.json(appointments);
  } catch (error) {
    console.error('Error fetching appointments:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to fetch appointments' },
      { status: 500 }
    );
  }
}
