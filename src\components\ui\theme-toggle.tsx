'use client';

import { useState, useEffect } from 'react';
import { Moon, Sun } from 'lucide-react';

export function ThemeToggle() {
  const [isDark, setIsDark] = useState(false);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    // Check localStorage and system preference on mount
    const savedTheme = localStorage.getItem('theme');
    const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
    const initialTheme = savedTheme || systemTheme;

    console.log('Theme initialization:', { savedTheme, systemTheme, initialTheme });

    const isDarkMode = initialTheme === 'dark';
    setIsDark(isDarkMode);
    applyTheme(isDarkMode);
    setMounted(true);
  }, []);

  const applyTheme = (dark: boolean) => {
    const root = document.documentElement;
    console.log('BEFORE - HTML classes:', root.className);
    console.log('BEFORE - Body background:', window.getComputedStyle(document.body).backgroundColor);

    if (dark) {
      root.classList.add('dark');
    } else {
      root.classList.remove('dark');
    }

    console.log('AFTER - HTML classes:', root.className);
    setTimeout(() => {
      console.log('AFTER - Body background:', window.getComputedStyle(document.body).backgroundColor);
    }, 100);
  };

  const toggleTheme = () => {
    const newIsDark = !isDark;
    console.log('Toggle clicked:', { currentIsDark: isDark, newIsDark });
    setIsDark(newIsDark);

    // Save to localStorage
    const themeValue = newIsDark ? 'dark' : 'light';
    localStorage.setItem('theme', themeValue);
    console.log('Saved to localStorage:', themeValue);

    // Apply theme
    applyTheme(newIsDark);
  };

  // Prevent hydration mismatch
  if (!mounted) {
    return (
      <div className="p-2 w-10 h-10 rounded-md bg-gray-200 dark:bg-gray-700"></div>
    );
  }

  return (
    <button
      onClick={toggleTheme}
      className="p-2 rounded-md bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
      aria-label={`Switch to ${isDark ? 'light' : 'dark'} mode`}
      style={{
        backgroundColor: isDark ? '#374151' : '#e5e7eb',
        color: isDark ? '#e5e7eb' : '#374151'
      }}
    >
      {isDark ? <Sun className="h-5 w-5" /> : <Moon className="h-5 w-5" />}
    </button>
  );
}
