import { NextRequest, NextResponse } from 'next/server';
import { writeFile, mkdir } from 'fs/promises';
import { existsSync } from 'fs';
import path from 'path';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const audioFile = formData.get('audio') as File;
    const metadata = formData.get('metadata') as string;
    
    if (!audioFile) {
      return NextResponse.json({ error: 'No audio file provided' }, { status: 400 });
    }
    
    const bytes = await audioFile.arrayBuffer();
    const buffer = Buffer.from(bytes);
    
    // First, try to save to network share
    const networkPath = '\\\\192.168.0.2\\share\\recordings';
    const localBackupPath = path.join(process.cwd(), 'voice-recordings');
    
    let savedToNetwork = false;
    let savedToLocal = false;
    
    try {
      // Try network share first
      await saveToNetworkShare(buffer, audioFile.name, networkPath);
      savedToNetwork = true;
    } catch (networkError) {
      console.log('Network share unavailable, saving locally:', networkError);
      
      // Fallback to local storage
      try {
        if (!existsSync(localBackupPath)) {
          await mkdir(localBackupPath, { recursive: true });
        }
        
        const localFilePath = path.join(localBackupPath, audioFile.name);
        await writeFile(localFilePath, buffer);
        savedToLocal = true;
      } catch (localError) {
        console.error('Failed to save locally:', localError);
        return NextResponse.json({ 
          error: 'Failed to save recording' 
        }, { status: 500 });
      }
    }
    
    return NextResponse.json({
      success: true,
      savedToNetwork,
      savedToLocal,
      filename: audioFile.name,
      size: buffer.length,
      metadata: metadata ? JSON.parse(metadata) : null
    });
    
  } catch (error) {
    console.error('Upload error:', error);
    return NextResponse.json({ 
      error: 'Upload failed' 
    }, { status: 500 });
  }
}

async function saveToNetworkShare(buffer: Buffer, filename: string, networkPath: string) {
  // This is a simplified implementation
  // In production, you would use proper Windows network share access
  // For now, we'll simulate by throwing an error if conditions aren't met
  
  const now = new Date();
  const hour = now.getHours();
  
  // Simulate network availability check
  if (hour < 7 || hour > 19) {
    throw new Error('Network share not available outside business hours');
  }
  
  // In production, use proper file operations for network shares:
  // const networkFilePath = path.join(networkPath, filename);
  // await writeFile(networkFilePath, buffer);
  
  console.log(`Would save ${filename} to network share at ${networkPath}`);
}
