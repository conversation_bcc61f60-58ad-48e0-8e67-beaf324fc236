import { NextRequest, NextResponse } from 'next/server';
import { AIService } from '@/lib/ai-service';
import { SikkaClient } from '@/lib/sikka-client';

export async function POST(request: NextRequest) {
  try {
    const { message, history } = await request.json();

    if (!message || typeof message !== 'string') {
      return NextResponse.json(
        { error: 'Message is required' },
        { status: 400 }
      );
    }

    // Check if OpenAI API key is configured
    if (!process.env.OPENAI_API_KEY) {
      return NextResponse.json({
        response: 'AI functionality is not currently available. Please contact your system administrator to configure the OpenAI API key.'
      });
    }

    console.log('AI Chat: Processing query:', message);

    // Step 1: Interpret the user's query
    const interpretation = await AIService.interpretQuery(message);
    console.log('AI Chat: Query interpretation:', interpretation);

    // Step 2: Fetch data based on AI's intelligent endpoint selection
    let rawData: any[] = [];
    let allData: any = {};

    try {
      const sikkaClient = new SikkaClient();

      // Parse date range from query for time-based data
      const endDate = new Date();
      const startDate = new Date();

      if (message.toLowerCase().includes('last month')) {
        startDate.setMonth(endDate.getMonth() - 1);
      } else if (message.toLowerCase().includes('last quarter')) {
        startDate.setMonth(endDate.getMonth() - 3);
      } else if (message.toLowerCase().includes('last year')) {
        startDate.setFullYear(endDate.getFullYear() - 1);
      } else if (message.toLowerCase().includes('this year')) {
        startDate.setMonth(0, 1);
      } else if (message.toLowerCase().includes('today')) {
        startDate.setHours(0, 0, 0, 0);
      } else if (message.toLowerCase().includes('this week')) {
        startDate.setDate(endDate.getDate() - 7);
      } else {
        startDate.setMonth(endDate.getMonth() - 3); // Default: last 3 months
      }

      const formattedStartDate = startDate.toISOString().split('T')[0];
      const formattedEndDate = endDate.toISOString().split('T')[0];

      console.log(`AI Chat: AI determined endpoints needed:`, interpretation.sikka_endpoints);

      // Fetch data from each endpoint the AI determined is needed
      for (const endpoint of interpretation.sikka_endpoints) {
        console.log(`AI Chat: Fetching data from ${endpoint}`);

        switch (endpoint) {
          case '/appointments':
            const appointments = await sikkaClient.getAppointments(formattedStartDate, formattedEndDate);
            allData.appointments = appointments || [];
            rawData = rawData.concat(appointments || []);
            console.log(`AI Chat: Retrieved ${appointments?.length || 0} appointments`);
            break;

          case '/patients':
            // For patient queries - fetch anonymized patient data
            console.log('AI Chat: Patient data requested - fetching anonymized patient information');
            const patients = await sikkaClient.getPatients(100);
            allData.patients = patients || [];
            // Don't add to rawData directly - will be anonymized separately
            break;

          case '/procedures':
            // Fetch dedicated procedure data
            console.log('AI Chat: Procedure data requested - fetching procedure information');
            const procedures = await sikkaClient.getProcedures(formattedStartDate, formattedEndDate);
            allData.procedures = procedures || [];
            rawData = rawData.concat(procedures || []);
            console.log(`AI Chat: Retrieved ${procedures?.length || 0} procedures`);
            break;

          case '/operatories':
            // Fetch operatory configuration data
            console.log('AI Chat: Operatory data requested - fetching operatory information');
            const operatories = await sikkaClient.getOperatories();
            allData.operatories = operatories || [];
            rawData = rawData.concat(operatories || []);
            console.log(`AI Chat: Retrieved ${operatories?.length || 0} operatories`);
            break;

          default:
            console.log(`AI Chat: Unknown endpoint ${endpoint} - defaulting to appointments`);
            if (!allData.appointments) {
              const appointments = await sikkaClient.getAppointments(formattedStartDate, formattedEndDate);
              allData.appointments = appointments || [];
              rawData = rawData.concat(appointments || []);
            }
        }
      }

      // If AI didn't specify endpoints or specified none, default to appointments
      if (interpretation.sikka_endpoints.length === 0) {
        console.log('AI Chat: No specific endpoints requested - fetching appointments as default');
        const appointments = await sikkaClient.getAppointments(formattedStartDate, formattedEndDate);
        allData.appointments = appointments || [];
        rawData = appointments || [];
        console.log(`AI Chat: Retrieved ${appointments?.length || 0} appointments as default`);
      }

      console.log(`AI Chat: Total data points collected: ${rawData.length}`);
      console.log(`AI Chat: Data sources used:`, Object.keys(allData));

    } catch (apiError) {
      console.error('AI Chat: Error fetching data from Sikka API:', apiError);
      return NextResponse.json({
        response: 'I encountered an error while fetching practice data. Please ensure your API connection is working properly.'
      });
    }

    // Step 3: Anonymize the data (remove all PHI)
    const anonymizedData = AIService.anonymizeData(rawData);
    console.log('AI Chat: Data anonymized, processing', anonymizedData.metadata.total_count, 'records');

    // Step 4: Generate aggregated statistics
    const stats = AIService.generateStats(rawData);
    console.log('AI Chat: Generated statistics:', Object.keys(stats));

    // Step 5: Generate AI response using anonymized data only
    const aiResponse = await AIService.generateResponse(message, anonymizedData, stats);
    console.log('AI Chat: Generated response length:', aiResponse.length);

    return NextResponse.json({
      response: aiResponse,
      metadata: {
        records_processed: anonymizedData.metadata.total_count,
        anonymization_applied: true,
        query_interpretation: interpretation.intent
      }
    });

  } catch (error) {
    console.error('AI Chat: Error processing request:', error);
    
    return NextResponse.json({
      response: 'I apologize, but I encountered an unexpected error while processing your request. Please try again or contact support if the issue persists.'
    }, { status: 500 });
  }
}

// Handle preflight requests for CORS
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}
