/**
 * Sikka API Client
 * Handles authentication and API requests to the Sikka API
 */

import { Appointment, AuthResponse, Credentials, MedicalNote, Operatory } from './types';

// API base URLs
const API_BASE_V2 = 'https://api.sikkasoft.com/v2';
const API_BASE_V4 = 'https://api.sikkasoft.com/v4';

// Default timeout for API requests (in milliseconds)
const API_TIMEOUT = 30000;

// Cache for API responses
const apiCache: Record<string, { data: any, timestamp: number }> = {};

// Helper function to add delay between API requests to avoid rate limiting
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

/**
 * Helper function to make API requests with caching
 */
async function cachedApiRequest<T>(
  cacheKey: string,
  requestFn: () => Promise<T>,
  cacheDuration: number = 5 * 60 * 1000 // 5 minutes by default
): Promise<T> {
  // Check if we have a valid cached response
  const cachedResponse = apiCache[cacheKey];
  if (cachedResponse && Date.now() - cachedResponse.timestamp < cacheDuration) {
    return cachedResponse.data as T;
  }

  // Add a small delay to avoid rate limiting
  await delay(300);

  // Make the actual request
  const data = await requestFn();

  // Cache the response
  apiCache[cacheKey] = {
    data,
    timestamp: Date.now()
  };

  return data;
}

/**
 * Sikka API Client class
 */
export class SikkaApiClient {
  private credentials: Credentials;
  private requestKey: string | null = null;

  constructor(credentials: Credentials) {
    this.credentials = credentials;
  }

  /**
   * Authenticate with Sikka API and get request key
   */
  async authenticate(): Promise<string> {
    try {
      const response = await fetch(`${API_BASE_V4}/request_key`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          grant_type: 'request_key',
          office_id: this.credentials.office_id,
          secret_key: this.credentials.secret_key,
          app_id: this.credentials.app_id,
          app_key: this.credentials.app_key,
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Authentication failed with status ${response.status}: ${errorText}`);
      }

      const data = await response.json() as AuthResponse;
      this.requestKey = data.request_key;

      if (!this.requestKey) {
        throw new Error('No request key in response');
      }

      return this.requestKey;
    } catch (error) {
      console.error('Error during authentication:', error);
      throw new Error(`Authentication failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Get the current request key or authenticate to get a new one
   */
  async getRequestKey(): Promise<string> {
    if (!this.requestKey) {
      return this.authenticate();
    }
    return this.requestKey;
  }

  /**
   * Fetch operatories for a specific date
   */
  async getOperatories(date: string): Promise<Operatory[]> {
    // Create a cache key based on date
    const cacheKey = `operatories-${date}`;

    // Check if we have a valid cached response (cache for 5 minutes)
    const cachedResponse = apiCache[cacheKey];
    if (cachedResponse && Date.now() - cachedResponse.timestamp < 5 * 60 * 1000) {
      console.log('Using cached operatories data');
      return cachedResponse.data as Operatory[];
    }

    // Add a small delay to avoid rate limiting
    await delay(500);

    const requestKey = await this.getRequestKey();

    try {
      // Use the appointments endpoint with date filtering to get only appointments for the specific date
      const response = await fetch(`${API_BASE_V2}/appointments?date=${date}&startdate=${date}&enddate=${date}&date_filter_on=appointment_date`, {
        headers: {
          'Request-Key': requestKey,
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch operatories: ${response.status}`);
      }

      const data = await response.json();
      console.log('Raw appointments data:', JSON.stringify(data, null, 2));

      // Extract operatories from appointments
      const operatories = new Set<string>();
      let appointmentCount = 0;

      if (Array.isArray(data) && data.length > 0 && data[0].items) {
        console.log(`Found ${data[0].items.length} appointments in the response`);
        
        for (const item of data[0].items) {
          const operatory = item.operatory;
          const appointmentDate = item.date || item.appointment_date;
          
          // Debug log for each appointment
          console.log('Processing appointment:', {
            patient: item.patient_name || 'unknown',
            operatory,
            date: appointmentDate,
            status: item.status,
            description: item.description
          });
          
          // Skip if no operatory or invalid operatory
          if (!operatory || operatory === '' || operatory === 'N/A') {
            console.log('Skipping - Invalid operatory');
            continue;
          }
          
          // Skip DAZ1 and DAZ2 operatories
          if (operatory === 'DAZ1' || operatory === 'DAZ2') {
            console.log('Skipping - DAZ1/DAZ2 operatory');
            continue;
          }
          
          // Verify the appointment is for the correct date
          if (appointmentDate && appointmentDate !== date) {
            console.log(`Skipping - Appointment date (${appointmentDate}) doesn't match requested date (${date})`);
            continue;
          }
          
          // Skip blocked or cancelled appointments
          if (item.status === 'Blocked' || item.status === 'Cancelled' || 
              (item.description && item.description.toLowerCase().includes('blocked'))) {
            console.log('Skipping - Blocked or cancelled appointment');
            continue;
          }
          
          console.log(`Adding operatory: ${operatory}`);
          operatories.add(operatory);
          appointmentCount++;
        }
      }
      
      console.log(`Found ${operatories.size} unique operatories with ${appointmentCount} valid appointments`);

      // Map operatory codes to provider names
      const operatoryToProvider: Record<string, string> = {
        'DL01': 'Dr. Lowell',
        'DL02': 'Dr. Lowell',
        'NS01': 'Dr. Soto',
        'NS02': 'Dr. Soto',
        'HYG1': 'Hygiene',
        'HYG2': 'Hygiene',
        'HYG3': 'Hygiene',
        'CONS': 'Consult Room',
        'LAB': 'Lab',
      };

      // Convert to array of Operatory objects with provider information
      const operatoryList = Array.from(operatories).map(name => {
        // Determine the provider group
        let provider = operatoryToProvider[name];

        // If no mapping exists, use the operatory name itself as the provider group
        if (!provider) {
          // Try to extract a provider name from the operatory code
          const match = name.match(/^([A-Za-z]+)\d*$/);
          if (match && match[1]) {
            provider = match[1].toUpperCase();
          } else {
            provider = name;
          }
        }

        
        return {
          id: name,
          name,
          provider,
          sortKey: name.toLowerCase(),
        };
      });

      // Sort operatories by provider first, then by name
      const sortedOperatories = operatoryList.sort((a, b) => {
        // First sort by provider
        if (a.provider !== b.provider) {
          return a.provider.localeCompare(b.provider);
        }
        // Then sort by operatory name
        return a.sortKey.localeCompare(b.sortKey);
      });

      console.log(`Found ${sortedOperatories.length} operatories with appointments for ${date}:`, 
        sortedOperatories.map(op => op.name).join(', '));

      // Cache the response
      apiCache[cacheKey] = {
        data: sortedOperatories,
        timestamp: Date.now()
      };

      return sortedOperatories;
    } catch (error) {
      console.error('Error fetching operatories:', error);
      throw new Error(`Failed to fetch operatories: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Fetch appointments for a specific date and operatories
   */
  async getAppointments(date: string, operatories?: string[]): Promise<Appointment[]> {
    // Create a cache key based on date and operatories (create a copy to avoid modifying the original)
    const operatoriesKey = operatories ? [...operatories].sort().join('-') : 'all';
    const cacheKey = `appointments-${date}-${operatoriesKey}`;

    // Log the request details
    console.log(`SikkaClient: Fetching appointments for date: ${date}, operatories: ${operatories ? operatories.join(', ') : 'all'}`);

    // Check if we have a valid cached response (cache for 5 minutes)
    const cachedResponse = apiCache[cacheKey];
    if (cachedResponse && Date.now() - cachedResponse.timestamp < 5 * 60 * 1000) {
      console.log('SikkaClient: Using cached appointments data');
      return cachedResponse.data as Appointment[];
    }

    // Add a small delay to avoid rate limiting
    await delay(500);

    const requestKey = await this.getRequestKey();

    try {
      // Use v2 appointments endpoint with date_filter_on=appointment_date to filter by appointment date
      // Also use startdate and enddate to ensure we only get appointments for the specific date
      const response = await fetch(`${API_BASE_V2}/appointments?date=${date}&startdate=${date}&enddate=${date}&date_filter_on=appointment_date`, {
        headers: {
          'Request-Key': requestKey,
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch appointments: ${response.status}`);
      }

      const data = await response.json();

      // Extract appointments
      const appointments: Appointment[] = [];

      if (Array.isArray(data) && data.length > 0 && data[0].items) {
        for (const item of data[0].items) {
          // Skip DAZ1 and DAZ2 operatories
          if (item.operatory === 'DAZ1' || item.operatory === 'DAZ2') {
            // console.log(`SikkaClient: Skipping appointment in operatory ${item.operatory}`);
            continue;
          }

          // Skip if operatory filter is provided and this appointment's operatory is not in the list
          if (operatories && operatories.length > 0 && !operatories.includes(item.operatory)) {
            console.log(`SikkaClient: Filtering out appointment in operatory ${item.operatory} (not in requested operatories: ${operatories.join(', ')})`);
            continue;
          }

          // Skip if the appointment date doesn't match the requested date
          // This is a double-check in case the API doesn't filter correctly
          if (item.date && item.date !== date) {
            console.log(`SikkaClient: Filtering out appointment on date ${item.date} (requested date: ${date})`);
            continue;
          }

          // Log that we're including this appointment
          console.log(`SikkaClient: Including appointment for ${item.patient_name || 'unknown'} in operatory ${item.operatory}`);

          // Log the appointment details for debugging with full provider information
          console.log(`PROVIDER INFO for ${item.patient_name}: ${JSON.stringify(item.provider)}`);
          console.log(`Appointment details: Start=${item.startTime}, End=${item.endTime}, Length=${item.length}`);


          // Determine if this is a blocked time slot
          const isBlocked = item.status === 'Blocked' ||
                           (item.description && item.description.toLowerCase().includes('blocked'));

          // Skip appointments that only say "Blocked" with no other information
          if (isBlocked && item.description && item.description.trim().toLowerCase() === 'blocked') {
            continue;
          }

          // Format the appointment
          appointments.push({
            id: item.appointment_sr_no || `${date}-${item.operatory}-${item.time}`,
            appointment_sr_no: item.appointment_sr_no,
            patient_id: item.patient_id,
            patient_name: item.patient_name || 'No Patient',
            provider: item.provider,
            operatory: item.operatory,
            date: item.date || date,
            startTime: item.time || '8:00 AM',
            endTime: item.end_time || '9:00 AM',
            length: item.length || 60,
            type: item.description || 'Unknown',
            description: item.description || '',
            isBlocked,
            status: item.status,
          });
        }
      }

      // Cache the response
      apiCache[cacheKey] = {
        data: appointments,
        timestamp: Date.now()
      };

      return appointments;
    } catch (error) {
      console.error('Error fetching appointments:', error);
      throw new Error(`Failed to fetch appointments: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Fetch medical notes for a specific date
   */
  async getMedicalNotes(date: string): Promise<MedicalNote[]> {
    // Create a cache key based on date
    const cacheKey = `medical-notes-${date}`;

    // Check if we have a valid cached response (cache for 5 minutes)
    const cachedResponse = apiCache[cacheKey];
    if (cachedResponse && Date.now() - cachedResponse.timestamp < 5 * 60 * 1000) {
      console.log('Using cached medical notes data');
      return cachedResponse.data as MedicalNote[];
    }

    // Add a small delay to avoid rate limiting
    await delay(500);

    const requestKey = await this.getRequestKey();

    try {
      const response = await fetch(`${API_BASE_V4}/medical_notes?date=${date}`, {
        headers: {
          'Request-Key': requestKey,
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch medical notes: ${response.status}`);
      }

      const data = await response.json();

      // Extract notes
      const notes: MedicalNote[] = [];

      if (data.items) {
        for (const item of data.items) {
          // Find note content in various possible fields
          let noteContent = '';
          for (const field of ['note', 'text', 'description']) {
            if (item[field] && item[field].trim()) {
              noteContent = item[field];
              break;
            }
          }

          if (noteContent) {
            notes.push({
              id: item.id || `${date}-${item.patient_id || 'unknown'}`,
              patient_id: item.patient_id,
              patient_name: item.patient_name,
              date: item.date || date,
              provider: item.provider,
              note: noteContent,
              note_type: item.note_type,
              created_date: item.created_date,
            });
          }
        }
      }

      // Cache the response
      apiCache[cacheKey] = {
        data: notes,
        timestamp: Date.now()
      };

      return notes;
    } catch (error) {
      console.error('Error fetching medical notes:', error);
      throw new Error(`Failed to fetch medical notes: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  async searchPatients(query: string): Promise<any[]> {
    try {
      console.log(`SikkaClient: Searching for patients with query: ${query}`);

      const requestKey = await this.getRequestKey();
      const url = `${API_BASE_V2}/patients`;
      const params = new URLSearchParams({
        search: query,
        limit: '50' // Limit to 50 results
      });

      const response = await fetch(`${url}?${params}`, {
        method: 'GET',
        headers: {
          'Request-Key': requestKey,
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log(`SikkaClient: Patient search response:`, data);

      // Extract patients from the response - the API returns an array with one object containing items
      let patients = [];
      if (Array.isArray(data) && data.length > 0 && data[0].items) {
        patients = data[0].items;
      } else if (data.items) {
        patients = data.items;
      } else if (data.data) {
        patients = data.data;
      }

      console.log(`SikkaClient: Extracted ${patients.length} patients from response`);

      // Log the first patient to see the structure
      if (patients.length > 0) {
        console.log(`SikkaClient: First patient structure:`, JSON.stringify(patients[0], null, 2));
      }

      // Format patient data
      const formattedPatients = patients.map((patient: any, index: number) => {
        console.log(`SikkaClient: Processing patient ${index}:`, JSON.stringify(patient, null, 2));

        // Extract name components
        const firstName = patient.firstname || patient.first_name || patient.firstName || '';
        const lastName = patient.lastname || patient.last_name || patient.lastName || '';
        const middleName = patient.middlename || patient.middle_name || patient.middleName || '';
        const preferredName = patient.preferred_name || patient.preferredName || '';

        // Create middle initial from middle name
        const middleInitial = middleName ? middleName.charAt(0).toUpperCase() : '';

        // Build the display name: "FirstName (PreferredName) MiddleInitial LastName"
        let displayName = '';
        const nameParts = [];

        // Add first name
        if (firstName && firstName.trim() !== '') {
          nameParts.push(firstName.trim());
        }

        // Add preferred name in parentheses if it exists and is different from first name
        if (preferredName && preferredName.trim() !== '' && preferredName.trim() !== firstName.trim()) {
          nameParts.push(`(${preferredName.trim()})`);
        }

        // Add middle initial
        if (middleInitial) {
          nameParts.push(middleInitial);
        }

        // Add last name
        if (lastName && lastName.trim() !== '') {
          nameParts.push(lastName.trim());
        }

        displayName = nameParts.join(' ').trim();

        // Calculate age from birthdate
        const birthDate = patient.birthdate || patient.date_of_birth || patient.dateOfBirth || patient.dob;
        let age = '';
        if (birthDate) {
          const today = new Date();
          const birth = new Date(birthDate);
          const ageYears = today.getFullYear() - birth.getFullYear();
          const monthDiff = today.getMonth() - birth.getMonth();

          if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
            age = `${ageYears - 1}`;
          } else {
            age = `${ageYears}`;
          }
        }

        // Get gender
        const gender = patient.gender || '';
        const genderInitial = gender ? gender.charAt(0).toUpperCase() : '';

        const formatted = {
          id: patient.id || patient.patient_id || patient.patient_sr_no,
          name: displayName,
          firstName: firstName,
          lastName: lastName,
          middleName: middleName,
          middleInitial: middleInitial,
          preferredName: preferredName,
          dateOfBirth: birthDate,
          age: age,
          gender: gender,
          genderInitial: genderInitial,
          phone: patient.cell || patient.homephone || patient.phone || patient.phone_number || patient.home_phone,
          email: patient.email || patient.email_address,
          lastVisit: patient.last_visit || patient.lastVisit || '',
          firstVisit: patient.first_visit || patient.firstVisit || ''
        };

        console.log(`SikkaClient: Formatted patient ${index}:`, formatted);
        return formatted;
      });

      // Sort patients by most recent visit date (newest first)
      const sortedPatients = formattedPatients.sort((a, b) => {
        const dateA = a.lastVisit ? new Date(a.lastVisit).getTime() : 0;
        const dateB = b.lastVisit ? new Date(b.lastVisit).getTime() : 0;

        // Sort by last visit date descending (newest first)
        // If no last visit date, put at the end
        if (dateA === 0 && dateB === 0) return 0;
        if (dateA === 0) return 1;
        if (dateB === 0) return -1;
        return dateB - dateA;
      });

      console.log(`SikkaClient: Found ${sortedPatients.length} patients, sorted by most recent visit`);
      return sortedPatients;

    } catch (error) {
      console.error('Error searching patients:', error);
      throw error;
    }
  }

  async getPatientClinicalNotes(patientId: string): Promise<any[]> {
    try {
      console.log(`SikkaClient: Fetching clinical notes for patient: ${patientId}`);

      const requestKey = await this.getRequestKey();

      // Try v4 API first for clinical notes
      const url = `${API_BASE_V4}/patients/${patientId}/clinical_notes`;
      const params = new URLSearchParams({
        limit: '10', // Last 10 visits
        sort: 'date_desc' // Most recent first
      });

      const response = await fetch(`${url}?${params}`, {
        method: 'GET',
        headers: {
          'Request-Key': requestKey,
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log(`SikkaClient: Clinical notes response:`, data);

      // Extract notes from the response
      const notes = data.data || data.notes || data.items || [];

      // Format clinical notes data
      const formattedNotes = notes.map((note: any) => ({
        id: note.id || note.note_id,
        date: note.date || note.visit_date || note.created_date,
        provider: note.provider || note.provider_name || 'Unknown Provider',
        notes: note.notes || note.clinical_notes || note.description || '',
        appointmentType: note.appointment_type || note.type,
        procedures: note.procedures || []
      }));

      console.log(`SikkaClient: Found ${formattedNotes.length} clinical notes`);
      return formattedNotes;

    } catch (error) {
      console.error('Error fetching clinical notes:', error);
      throw error;
    }
  }

  async getPatientRecentVisitCount(patientId: string, months: number = 6): Promise<number> {
    try {
      console.log(`SikkaClient: Getting visit count for patient ${patientId} in last ${months} months`);

      const requestKey = await this.getRequestKey();

      // Calculate date range (last 6 months)
      const endDate = new Date();
      const startDate = new Date();
      startDate.setMonth(startDate.getMonth() - months);

      const startDateStr = startDate.toISOString().split('T')[0];
      const endDateStr = endDate.toISOString().split('T')[0];

      // Get appointments for this patient in the date range
      const url = `${API_BASE_V2}/appointments`;
      const params = new URLSearchParams({
        patient_id: patientId,
        startdate: startDateStr,
        enddate: endDateStr,
        date_filter_on: 'appointment_date'
      });

      const response = await fetch(`${url}?${params}`, {
        method: 'GET',
        headers: {
          'Request-Key': requestKey,
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      // Count completed appointments
      let visitCount = 0;
      if (Array.isArray(data) && data.length > 0 && data[0].items) {
        visitCount = data[0].items.filter((item: any) =>
          item.status !== 'Cancelled' &&
          item.status !== 'No Show' &&
          item.status !== 'Scheduled'
        ).length;
      }

      console.log(`SikkaClient: Found ${visitCount} visits for patient ${patientId} in last ${months} months`);
      return visitCount;

    } catch (error) {
      console.error('Error getting patient visit count:', error);
      return 0; // Return 0 if we can't get the count
    }
  }

  async getPatientVisits(patientId: string): Promise<any[]> {
    try {
      console.log(`SikkaClient: Fetching visits for patient: ${patientId}`);

      const requestKey = await this.getRequestKey();

      // Get appointments for this patient (all time)
      const url = `${API_BASE_V2}/appointments`;
      const params = new URLSearchParams({
        patient_id: patientId,
        limit: '100' // Get up to 100 visits
      });

      const response = await fetch(`${url}?${params}`, {
        method: 'GET',
        headers: {
          'Request-Key': requestKey,
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log(`SikkaClient: Patient visits response:`, data);

      // Extract visits from the response
      let visits = [];
      if (Array.isArray(data) && data.length > 0 && data[0].items) {
        visits = data[0].items;
      } else if (data.items) {
        visits = data.items;
      } else if (data.data) {
        visits = data.data;
      }

      // Format visit data and filter out cancelled/no-shows
      const formattedVisits = visits
        .filter((visit: any) =>
          visit.status !== 'Cancelled' &&
          visit.status !== 'No Show'
        )
        .map((visit: any, index: number) => ({
          id: visit.id || visit.appointment_id || `visit-${patientId}-${index}-${Date.now()}`,
          date: visit.date || visit.appointment_date,
          provider: typeof visit.provider === 'string' ? visit.provider : (visit.provider?.name || 'Unknown Provider'),
          appointmentType: typeof visit.type === 'string' ? visit.type : (visit.appointment_type || 'General'),
          status: typeof visit.status === 'string' ? visit.status : 'Completed',
          operatory: typeof visit.operatory === 'string' ? visit.operatory : (visit.operatory?.name || visit.operatory)
        }))
        .sort((a: any, b: any) => {
          // Sort by date descending (most recent first)
          const dateA = new Date(a.date).getTime();
          const dateB = new Date(b.date).getTime();
          return dateB - dateA;
        });

      console.log(`SikkaClient: Found ${formattedVisits.length} visits for patient ${patientId}`);
      return formattedVisits;

    } catch (error) {
      console.error('Error fetching patient visits:', error);
      throw error;
    }
  }


}
