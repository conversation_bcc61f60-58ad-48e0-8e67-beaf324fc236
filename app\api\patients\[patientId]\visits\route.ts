import { NextRequest, NextResponse } from 'next/server';
import { SikkaApiClient } from '@/lib/api/sikka-client';
import { loadCredentials } from '@/lib/api/credentials';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ patientId: string }> }
) {
  try {
    const { patientId } = await params;

    if (!patientId) {
      return NextResponse.json(
        { error: 'Patient ID is required' },
        { status: 400 }
      );
    }

    console.log(`API: Fetching visits for patient: ${patientId}`);

    const credentials = loadCredentials();
    const sikkaClient = new SikkaApiClient(credentials);
    await sikkaClient.authenticate();
    const visits = await sikkaClient.getPatientVisits(patientId);

    console.log(`API: Found ${visits.length} visits for patient ${patientId}`);

    return NextResponse.json({
      visits,
      total: visits.length,
      patientId
    });

  } catch (error) {
    console.error('Patient visits API error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch patient visits' },
      { status: 500 }
    );
  }
}
