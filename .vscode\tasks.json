{"version": "2.0.0", "options": {"cwd": "c:\\temp-dental"}, "tasks": [{"label": "Launch Audio Transfer GUI", "type": "shell", "command": "python ${workspaceFolder}/archive/audio_transfer_gui.py", "presentation": {"reveal": "always", "panel": "shared"}, "problemMatcher": []}, {"label": "Start Next.js Server", "type": "shell", "command": "npm run dev", "presentation": {"reveal": "always", "panel": "shared"}, "problemMatcher": []}, {"label": "Open Schedule in Simple Browser", "type": "shell", "command": "cmd /c start vscode://simpleBrowser/open?url=http://localhost:3001", "presentation": {"reveal": "silent", "panel": "shared"}, "problemMatcher": []}]}