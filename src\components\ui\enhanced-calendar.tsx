"use client";

import { useState, useEffect } from "react";
import { format, addDays, subDays, startOfWeek, endOfWeek, eachDayOfInterval, isSameDay } from "date-fns";

interface EnhancedCalendarProps {
  initialDate?: Date;
  onDateChange: (date: Date) => void;
}

export function EnhancedCalendar({ initialDate = new Date(), onDateChange }: EnhancedCalendarProps) {
  console.log(`EnhancedCalendar: initialDate = ${initialDate.toISOString()}`);

  // Use useEffect to update the selected date when initialDate changes
  const [selectedDate, setSelectedDate] = useState(initialDate);
  const [currentWeekStart, setCurrentWeekStart] = useState(startOfWeek(initialDate, { weekStartsOn: 0 }));
  const [isDarkMode, setIsDarkMode] = useState(false);

  // Check for dark mode
  useEffect(() => {
    const checkDarkMode = () => {
      setIsDarkMode(document.documentElement.classList.contains('dark'));
    };

    checkDarkMode();

    // Watch for dark mode changes
    const observer = new MutationObserver(checkDarkMode);
    observer.observe(document.documentElement, { attributes: true, attributeFilter: ['class'] });

    return () => observer.disconnect();
  }, []);

  // Update selected date when initialDate prop changes
  useEffect(() => {
    console.log(`EnhancedCalendar: initialDate changed to ${initialDate.toISOString()}`);
    // Use functional updates to avoid stale closures
    setSelectedDate(() => initialDate);
    setCurrentWeekStart(() => startOfWeek(initialDate, { weekStartsOn: 0 }));
  }, [initialDate]);

  // Generate days for the current week
  const daysOfWeek = eachDayOfInterval({
    start: currentWeekStart,
    end: endOfWeek(currentWeekStart, { weekStartsOn: 0 })
  });

  // Handle date selection
  const handleDateSelect = (date: Date) => {
    console.log(`EnhancedCalendar: handleDateSelect called with date = ${date.toISOString()}`);
    setSelectedDate(date);
    onDateChange(date);
  };

  // Navigate to previous week
  const goToPreviousWeek = () => {
    setCurrentWeekStart(prevWeekStart => {
      const newWeekStart = subDays(prevWeekStart, 7);
      return newWeekStart;
    });
  };

  // Navigate to next week
  const goToNextWeek = () => {
    setCurrentWeekStart(prevWeekStart => {
      const newWeekStart = addDays(prevWeekStart, 7);
      return newWeekStart;
    });
  };

  // Go to today
  const goToToday = () => {
    const today = new Date();
    setSelectedDate(today);
    setCurrentWeekStart(startOfWeek(today, { weekStartsOn: 0 }));
    onDateChange(today);
  };

  // Day of week labels
  const dayLabels = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];

  return (
    <div
      className="rounded-lg p-4 border"
      style={
        isDarkMode
          ? { backgroundColor: '#374151', borderColor: '#4b5563', boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2)' }
          : { backgroundColor: '#e5e7eb', borderColor: '#d1d5db', boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)' }
      }
    >
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          {format(currentWeekStart, "MMMM yyyy")}
        </h3>
        <div className="flex space-x-2">
          <button
            onClick={goToPreviousWeek}
            className="p-2 rounded-md bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-200 shadow-sm"
            aria-label="Previous week"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
            </svg>
          </button>
          <button
            onClick={goToToday}
            className="px-3 py-2 rounded-md bg-blue-600 hover:bg-blue-700 text-white text-sm shadow-sm"
          >
            Today
          </button>
          <button
            onClick={goToNextWeek}
            className="p-2 rounded-md bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-200 shadow-sm"
            aria-label="Next week"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
            </svg>
          </button>
        </div>
      </div>

      {/* Day labels */}
      <div className="grid grid-cols-7 gap-1 mb-1">
        {dayLabels.map((day, index) => (
          <div
            key={index}
            className="text-center text-sm font-medium text-gray-500 dark:text-gray-400 py-1"
          >
            {day}
          </div>
        ))}
      </div>

      {/* Calendar days */}
      <div className="grid grid-cols-7 gap-1">
        {daysOfWeek.map((day, index) => {
          const isToday = isSameDay(day, new Date());
          const isSelected = isSameDay(day, selectedDate);
          const isWeekend = day.getDay() === 0 || day.getDay() === 6; // Sunday = 0, Saturday = 6

          return (
            <button
              key={index}
              onClick={() => handleDateSelect(day)}
              className="h-16 flex flex-col items-center justify-center rounded-md text-sm font-medium shadow-md transition-colors"
              style={
                isSelected
                  ? { backgroundColor: '#2563eb', color: 'white' }
                  : isToday
                    ? isDarkMode
                      ? { backgroundColor: '#1e3a8a', color: '#93c5fd' }
                      : { backgroundColor: '#dbeafe', color: '#3b82f6' }
                    : isWeekend
                      ? isDarkMode
                        ? { backgroundColor: '#1f2937', color: '#9ca3af' }
                        : { backgroundColor: '#f3f4f6', color: '#6b7280' }
                      : isDarkMode
                        ? { backgroundColor: '#374151', color: '#e5e7eb' }
                        : { backgroundColor: '#d1d5db', color: '#374151' }
              }
            >
              <span className="text-lg">{format(day, "d")}</span>
              <span className="text-xs">{format(day, "EEE")}</span>
            </button>
          );
        })}
      </div>

      {/* Selected date display */}
      <div className="mt-4 text-center text-gray-700 dark:text-gray-300">
        Selected: <span className="font-semibold">{format(selectedDate, "EEEE, MMMM d, yyyy")}</span>
      </div>
    </div>
  );
}
